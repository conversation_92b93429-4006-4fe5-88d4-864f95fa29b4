/**
 * Closet Screen Component
 *
 * Uses Meteor methods for backend communication:
 * - 'items-fetchAll': Fetches all clothing items
 * - 'itemCategories-fetch': Fetches categories for filtering
 */

import React, { useState, useEffect } from "react";
import { useRouter } from "expo-router";
import {
  ActivityIndicator,
  ScrollView,
  Alert,
  Text,
  View,
  Platform,
} from "react-native";
import { SearchIcon, PlusIcon, Filter } from "lucide-react-native";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { isTablet } from "@/constants/responsive";

// Define a blurhash placeholder for better loading experience
const PLACEHOLDER_BLURHASH = "L6PZfSi_.AyE_3t7t7R**0o#DgR4";
// Using direct Meteor calls instead of the getCategories helper
import Meteor from "@meteorrn/core";
import {
  addItem,
  getClothes,
  deleteItem,
  UploadImage,
  uploadImageToS3,
} from "@/methods/cloths";
import { getOutfits, deleteOutfit } from "@/methods/outfits";
import * as ImagePicker from "expo-image-picker";
import { getUserProfile } from "@/methods/users";
import { fetchCategoriesFromBackend } from "@/data/gender-categories";
import { getGenderSpecificCategoriesFromBackend } from "@/data/categories";
import {
  getStandardizedCategories,
  STANDARD_CATEGORIES,
} from "@/utils/standardCategories";
// Filter modals temporarily disabled
// import FilterModal from './FilterModal';
// import CategoryFilterModal from '@/components/CategoryFilterModal';
import ClosetBottomSheetModal from "./ClosetBottomSheetModal";
import PlanOutfitModal from "./PlanOutfitModal";
import { CollageEditOutfitModal } from "./CollageEditOutfitModal";
import AddClothsModal from "@/components/AddClothsModal";
import SideFilterPanel from "@/components/common/SideFilterPanel";
import DeleteItemModal from "@/components/DeleteItemModal";
import {
  HeaderContainer,
  HeaderTitle,
  IconButton,
  ActionButtonsContainer,
  ContentContainer,
  ToggleContainer,
  ToggleButtonsGroup,
  ToggleButton,
  ToggleButtonText,
  FilterButton,
  StatusText,
  ItemCard,
  ItemImage,
  ItemNameContainer,
  ItemNameText,
  ItemCategoryText,
  GridContainer,
  EmptyStateContainer,
  EmptyStateTitle,
  EmptyStateImage,
  EmptyStateText,
  ActionButton,
  ActionButtonText,
} from "./styles";
import SimpleAddItemModal from "./SimpleAddItemModal";
import AddClothesFromGalleryModal from "@/components/AddClothesFromGalleryModal";
// Bottom sheet implementation removed for now

export default function Closet() {
  const router = useRouter();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("Outfits");
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isFilterModalVisible, setIsFilterModalVisible] = useState(false);
  const [isAddClothesModalVisible, setIsAddClothesModalVisible] =
    useState(false);
  const [isPlanOutfitModalVisible, setIsPlanOutfitModalVisible] =
    useState(false);
  const [editingOutfit, setEditingOutfit] = useState<any>(null);
  const [isCollageModalVisible, setIsCollageModalVisible] = useState(false);

  console.log(
    "🏠 Closet - Step 1 - isCollageModalVisible:",
    isCollageModalVisible
  );

  // Track state changes
  useEffect(() => {
    console.log(
      "🏠 Closet - Step 1 - isCollageModalVisible changed to:",
      isCollageModalVisible
    );
  }, [isCollageModalVisible]);

  // Separate state variables for different modals
  const [isBottomSheetModalVisible, setIsBottomSheetModalVisible] =
    useState(false); // For ClosetBottomSheetModal
  const [isAddClothsModalVisible, setIsAddClothsModalVisible] = useState(false); // For AddClothsModal
  const [
    isAddClothesFromGalleryModalVisible,
    setIsAddClothesFromGalleryModalVisible,
  ] = useState(false); // For AddClothesFromGalleryModal

  // State for pre-selected image from gallery
  const [preSelectedImage, setPreSelectedImage] = useState<
    { uri: string; base64?: string } | undefined
  >(undefined);

  // Delete item modal state
  const [isDeleteModalVisible, setIsDeleteModalVisible] = useState(false);
  const [selectedItemToDelete, setSelectedItemToDelete] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Log state changes for debugging
  useEffect(() => {
    console.log(
      "isBottomSheetModalVisible changed:",
      isBottomSheetModalVisible
    );
  }, [isBottomSheetModalVisible]);

  useEffect(() => {
    console.log("isAddClothsModalVisible changed:", isAddClothsModalVisible);
  }, [isAddClothsModalVisible]);
  // Default filter is 'All' for both tabs
  const [selectedFilter, setSelectedFilter] = useState("All");

  // Delete modal state removed for now

  // Use backend API to fetch outfits
  const outfitsQuery = getOutfits({
    // Add category filtering if needed in the future
    // categoryName: selectedFilter !== 'All' ? selectedFilter : undefined
  });

  // Use the standardized getClothes function with the selected filter
  // We'll fetch all clothes and filter them client-side for more reliable filtering
  const { data: allClothesData, isLoading: clothesLoading } = getClothes();

  // Helper function to get the display category name
  const getCategoryDisplayName = (item: any): string => {
    // Use category.mainCategory field (current backend schema)
    if (item.category?.mainCategory) {
      return item.category.mainCategory;
    }

    // Fallback: Try legacy category.name field
    if (item.category?.name) {
      return item.category.name;
    }

    // Try mainCategory field directly on item (if it exists)
    if (item.mainCategory) {
      return item.mainCategory;
    }

    // If we have an itemCategoryId, try to map it using a comprehensive mapping approach
    if (item.itemCategoryId) {
      // Enhanced category mapping that covers more possibilities
      const comprehensiveCategoryMap: {[key: string]: string} = {
        // Direct matches
        'tops': 'Tops',
        'bottoms': 'Bottoms',
        'shoes': 'Shoes',
        'dresses': 'Dresses',
        'accessories': 'Accessories',
        'outerwear': 'Outerwear',
        'loungewear': 'Loungewear',
        'activewear': 'Activewear',
        'swimwear': 'Swimwear',
        'underwear': 'Underwear',
        'sleepwear': 'Sleepwear',
        'jewelry': 'Jewelry',
        'bags': 'Bags',
        'tech': 'Tech',
        'others': 'Others',

        // Common variations and backend IDs
        'top': 'Tops',
        'bottom': 'Bottoms',
        'shoe': 'Shoes',
        'dress': 'Dresses',
        'accessory': 'Accessories',
        'outer': 'Outerwear',
        'jacket': 'Outerwear',
        'coat': 'Outerwear',
        'sweater': 'Tops',
        'shirt': 'Tops',
        'blouse': 'Tops',
        'pants': 'Bottoms',
        'jeans': 'Bottoms',
        'skirt': 'Bottoms',
        'shorts': 'Bottoms',
        'sneakers': 'Shoes',
        'boots': 'Shoes',
        'sandals': 'Shoes',
        'bag': 'Bags',
        'purse': 'Bags',
        'backpack': 'Bags',
        'jewellery': 'Jewelry',
        'watch': 'Jewelry',
        'necklace': 'Jewelry',
        'earrings': 'Jewelry',
        'bracelet': 'Jewelry',
        'ring': 'Jewelry'
      };

      const itemCategoryLower = item.itemCategoryId.toLowerCase();

      // First try exact match
      if (comprehensiveCategoryMap[itemCategoryLower]) {
        return comprehensiveCategoryMap[itemCategoryLower];
      }

      // Then try partial matches
      for (const [key, value] of Object.entries(comprehensiveCategoryMap)) {
        if (itemCategoryLower.includes(key) || key.includes(itemCategoryLower)) {
          return value;
        }
      }

      // If no match found, return the itemCategoryId with proper capitalization
      return item.itemCategoryId.charAt(0).toUpperCase() + item.itemCategoryId.slice(1).toLowerCase();
    }

    // Fallback to 'Uncategorized' if no category information is available
    return 'Uncategorized';
  };

  // Client-side filtering of clothes based on selected category
  const clothesData = React.useMemo(() => {
    console.log("\n🔍 CLOTHES FILTERING DEBUG 🔍");
    console.log("Selected Filter:", selectedFilter);
    console.log("All Clothes Data:", allClothesData);

    if (!allClothesData) {
      console.log("❌ No allClothesData available");
      return { items: [] };
    }

    if (!allClothesData.items) {
      console.log("❌ No items in allClothesData");
      return { items: [] };
    }

    console.log(
      "📊 Total items in allClothesData:",
      allClothesData.items.length
    );

    // Log all items with their categories
    allClothesData.items.forEach((item, index) => {
      console.log(`Item ${index + 1}:`, {
        id: item._id,
        name: item.name,
        category: item.category,
        categoryName: item.categoryName,
        itemCategoryId: item.itemCategoryId,
      });
    });

    // If "All" is selected, return all items
    if (selectedFilter === "All") {
      console.log(
        '✅ Filter is "All", returning all items:',
        allClothesData.items.length
      );
      return allClothesData;
    }

    const normalizeCategoryForComparison = (categoryInput: any): string => {
      let categoryString = "";

      if (typeof categoryInput === "string") {
        categoryString = categoryInput;
      } else if (categoryInput?.name) {
        categoryString = categoryInput.name;
      } else if (categoryInput?.category) {
        categoryString = categoryInput.category;
      } else {
        return "";
      }

      return categoryString
        .replace(/^(category-|standard-|basic-)/, "")
        .replace(/-/g, " ")
        .toLowerCase()
        .trim();
    };

    const categoriesMatch = (
      itemCategory: string,
      filterCategory: string
    ): boolean => {
      console.log(
        `🔍 Matching categories: item="${itemCategory}" vs filter="${filterCategory}"`
      );

      if (filterCategory === "All") {
        console.log('✅ Filter is "All", match = true');
        return true;
      }

      if (!itemCategory || !filterCategory) {
        console.log("❌ Missing category data, match = false");
        return false;
      }

      const normalizedItemCategory =
        normalizeCategoryForComparison(itemCategory);
      const normalizedFilterCategory =
        normalizeCategoryForComparison(filterCategory);

      console.log(
        `Normalized: item="${normalizedItemCategory}" vs filter="${normalizedFilterCategory}"`
      );

      if (normalizedItemCategory === normalizedFilterCategory) {
        console.log("✅ Exact match found");
        return true;
      }

      if (
        normalizedItemCategory.endsWith("s") &&
        normalizedItemCategory.slice(0, -1) === normalizedFilterCategory
      ) {
        console.log("✅ Plural/singular match found (item plural)");
        return true;
      }

      if (
        normalizedFilterCategory.endsWith("s") &&
        normalizedFilterCategory.slice(0, -1) === normalizedItemCategory
      ) {
        console.log("✅ Plural/singular match found (filter plural)");
        return true;
      }

      const commonCategoryMappings: { [key: string]: string[] } = {
        tops: [
          "top", "tops", "shirt", "shirts", "blouse", "blouses",
          "sweaters", "sweater", "outerwear", "matching sets"
        ],
        bottoms: [
          "bottom", "bottoms", "pants", "pant", "skirt", "skirts", "loungewear"
        ],
        shoes: ["shoe", "shoes", "footwear", "sneakers", "boots"],
        dresses: ["dress", "dresses"],
        accessories: ["accessory", "accessories", "tech"],
        // Add mappings for new standardized categories to old categories
        "matching sets": ["matching sets", "matching set", "sets", "set"],
        sweaters: ["sweaters", "sweater", "tops", "top"], // Sweaters might be categorized as tops
        outerwear: [
          "outerwear",
          "outer wear",
          "jackets",
          "jacket",
          "coats",
          "coat",
          "tops",
        ],
        loungewear: [
          "loungewear",
          "lounge wear",
          "lounging",
          "casual",
          "tops",
          "bottoms",
        ],
        tech: ["tech", "technology", "electronics", "accessories"],
      };

      for (const [baseCategory, variations] of Object.entries(
        commonCategoryMappings
      )) {
        if (
          normalizedFilterCategory === baseCategory ||
          variations.includes(normalizedFilterCategory)
        ) {
          if (
            variations.includes(normalizedItemCategory) ||
            normalizedItemCategory === baseCategory
          ) {
            console.log(`✅ Category mapping match found: ${baseCategory}`);
            return true;
          }
        }
      }

      console.log("❌ No match found");
      return false;
    };

    console.log("🔍 Starting filtering with selectedFilter:", selectedFilter);

    const filteredItems = allClothesData.items.filter(
      (item: any, index: number) => {
        let itemCategoryInput = null;

        if (item.category) {
          itemCategoryInput = item.category;
        } else if (item.categoryName) {
          itemCategoryInput = item.categoryName;
        } else if (item.itemCategoryId) {
          itemCategoryInput = item.itemCategoryId;
        }

        console.log(`Filtering item ${index + 1} (${item.name}):`, {
          itemCategoryInput,
          selectedFilter,
          hasCategory: !!item.category,
          hasCategoryName: !!item.categoryName,
          hasItemCategoryId: !!item.itemCategoryId,
        });

        if (!itemCategoryInput) {
          console.log(`❌ Item ${index + 1} has no category, excluding`);
          return false;
        }

        const matches = categoriesMatch(itemCategoryInput, selectedFilter);
        console.log(
          `${matches ? "✅" : "❌"} Item ${index + 1} (${item.name}) ${
            matches ? "matches" : "does not match"
          } filter`
        );

        return matches;
      }
    );

    console.log("📊 Filtering results:");
    console.log("- Total items before filtering:", allClothesData.items.length);
    console.log("- Total items after filtering:", filteredItems.length);
    console.log("- Selected filter:", selectedFilter);

    return { ...allClothesData, items: filteredItems };
  }, [allClothesData, selectedFilter]);

  // Add enabled option to only fetch when Clothes tab is active
  useEffect(() => {
    if (activeTab === "Clothes") {
      // Force a refresh of the clothes data when the Clothes tab becomes active
      queryClient.invalidateQueries({ queryKey: ["clothes"] });

      // Add a small delay before refetching to ensure the cache is properly invalidated
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ["clothes"] });
      }, 300);
    }
  }, [activeTab, selectedFilter]);

  // We'll use Tanstack Query for categories instead of useState and useEffect

  // Default outfit categories if backend doesn't provide them
  const defaultOutfitCategories = [
    "All",
    "Work Wear",
    "Casual Wear",
    "Evening Wear",
    "Formal Wear",
    "Resort / Beach Wear",
    "Winter Wear",
  ];

  // Default clothes categories if backend doesn't provide them
  const defaultClothesCategories = ["All", "Tops", "Bottoms", "Shoes"];

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  // Using Tanstack Query for categories with gender-specific data
  const categoriesQuery = useQuery({
    queryKey: ["categories", userProfile?.data?.profile?.gender],
    queryFn: async () => {
      try {
        // Get the user's gender from the profile
        const gender = userProfile?.data?.profile?.gender;
        console.log("User gender for categories:", gender);

        if (!gender) {
          console.warn("No gender available, using default categories");
          return {
            standardizedCategories: STANDARD_CATEGORIES.map((name) => ({
              name,
              id: `standard-${name.toLowerCase().replace(/\s+/g, "-")}`,
            })),
          };
        }

        // Get standardized categories that include both backend and standard categories
        console.log("\n🔍 CLOSET SCREEN - LOADING STANDARDIZED CATEGORIES 🔍");
        const standardizedCategories = await getStandardizedCategories(gender);
        console.log(
          `Loaded ${standardizedCategories.length} standardized categories`
        );

        return { standardizedCategories };
      } catch (error) {
        console.error("Error in categoriesQuery:", error);
        return {
          standardizedCategories: STANDARD_CATEGORIES.map((name) => ({
            name,
            id: `standard-${name.toLowerCase().replace(/\s+/g, "-")}`,
          })),
        };
      }
    },
    select: (data: any) => {
      console.log("Processing standardized categories data");

      // Extract category names from standardized categories
      let outfitCats = defaultOutfitCategories;
      let clothesCats = ["All"]; // Start with 'All'

      if (
        data?.standardizedCategories &&
        data.standardizedCategories.length > 0
      ) {
        // Extract category names
        const categoryNames = data.standardizedCategories
          .map((category: any) => category.name)
          .filter((name: string | undefined | null) => name); // Remove undefined/null values

        console.log(
          `Using ${categoryNames.length} standardized category names`
        );

        // Add all standardized category names to clothes categories
        clothesCats = ["All", ...categoryNames];

        // Log the final categories that will be used
        console.log("=== FINAL CATEGORIES USED IN CLOSET SCREEN ===");
        clothesCats.forEach((cat, index) => {
          console.log(`${index}. ${cat}`);
        });
        console.log("=== END FINAL CATEGORIES ===");
      }

      return {
        outfitCategories: outfitCats,
        clothesCategories: clothesCats,
      };
    },
    // Refetch when user profile changes (which includes gender)
    enabled: !!userProfile,
  });

  // Extract the categories data from the query
  const {
    outfitCategories = defaultOutfitCategories,
    clothesCategories = defaultClothesCategories,
  } = categoriesQuery.data || {};

  // Determine which items to display based on the active tab
  const items =
    activeTab === "Outfits"
      ? outfitsQuery.data?.items || []
      : clothesData?.items || []; // Using the standardized clothesData structure

  // Force a refresh of the clothes data when the component mounts
  useEffect(() => {
    // Clear the cache and refetch the data
    queryClient.invalidateQueries({ queryKey: ["clothes"] });

    // Add a small delay before refetching to ensure the cache is properly invalidated
    setTimeout(() => {
      queryClient.refetchQueries({ queryKey: ["clothes"] });
    }, 300);
  }, []);

  // Determine loading state based on the active tab
  const isLoading =
    activeTab === "Outfits" ? outfitsQuery.isLoading : clothesLoading;

  // Handler for when a new item is added to the closet
  const handleItemAdded = () => {
    // Invalidate and refetch clothes queries
    queryClient.invalidateQueries({ queryKey: ["clothes"] });
  };

  // Setup mutations for image upload and item creation
  const { mutate: uploadImageMutation } = UploadImage();
  const { mutate: uploadImageToS3Mutation } = uploadImageToS3();
  const { mutate: addItemMutation } = addItem();

  // Function to access photo gallery and upload images - copied from AddItems
  const accessPhotoGallery = async () => {
    console.log("📸 Starting photo gallery access...");
    try {
      // Request permissions first
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      console.log("📸 Permission status:", status);

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please allow access to your photo library to select images."
        );
        return;
      }

      console.log("📸 Launching image library...");
      // No permissions request is necessary for launching the image library
      let result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ["images"],
        allowsEditing: false,
        base64: true,
        aspect: [4, 3],
        quality: 1,
      });

      console.log("📸 ImagePicker result:", {
        canceled: result.canceled,
        assetsLength: result.assets?.length,
      });

      if (!result.canceled) {
        console.log("📸 Image selected, setting preSelectedImage");
        setPreSelectedImage({
          uri: result.assets[0].uri,
          base64: result.assets[0].base64 || undefined,
        });

        console.log("📸 Opening AddClothesFromGalleryModal");
        // Open the new modal to add item details
        setIsAddClothesFromGalleryModalVisible(true);
      } else {
        console.log("📸 User canceled image selection");
      }
    } catch (error) {
      console.error("📸 Error accessing photo gallery:", error);
      Alert.alert("Error", "Failed to access photo gallery. Please try again.");
    }
  };

  // Function to access camera and take photos
  const accessCamera = async () => {
    console.log("📷 Starting camera access...");
    try {
      // Request camera permissions first
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      console.log("📷 Camera permission status:", status);

      if (status !== "granted") {
        Alert.alert(
          "Permission Required",
          "Please allow camera access to take photos."
        );
        return;
      }

      console.log("📷 Launching camera...");
      // Launch camera directly
      let result = await ImagePicker.launchCameraAsync({
        mediaTypes: ["images"],
        allowsEditing: false,
        base64: true,
        aspect: [4, 3],
        quality: 1,
      });

      console.log("📷 Camera result:", {
        canceled: result.canceled,
        assetsLength: result.assets?.length,
      });

      if (!result.canceled) {
        console.log("📷 Photo taken successfully");
        setPreSelectedImage({
          uri: result.assets[0].uri,
          base64: result.assets[0].base64 || undefined,
        });

        console.log("📷 Opening AddClothesFromGalleryModal");
        // Open the new modal to add item details
        setIsAddClothesFromGalleryModalVisible(true);
      } else {
        console.log("📷 Camera was canceled");
      }
    } catch (error) {
      console.error("📷 Error accessing camera:", error);
      Alert.alert("Error", "Failed to access camera. Please try again.");
    }
  };

  // Delete item handler - handles both clothes and outfits
  const handleDeleteItem = (item: any) => {
    if (item._id) {
      setSelectedItemToDelete(item);
      setIsDeleteModalVisible(true);
    }
  };

  // Create the delete mutation hooks at the component level
  const deleteItemMutation = deleteItem();
  const deleteOutfitMutation = deleteOutfit();

  // Confirm delete item - handles both clothes and outfits
  const confirmDeleteItem = async () => {
    if (!selectedItemToDelete || !selectedItemToDelete._id) return;

    setIsDeleting(true);
    try {
      if (selectedItemToDelete.isOutfit) {
        // Delete outfit using outfit API
        console.log("Deleting outfit:", selectedItemToDelete.name);
        await deleteOutfitMutation.mutateAsync(selectedItemToDelete._id);

        // Show success message
        Alert.alert(
          "Success",
          `Outfit "${selectedItemToDelete.name}" deleted successfully`
        );

        // Refresh the outfits list
        queryClient.invalidateQueries({ queryKey: ["outfits"] });
      } else {
        // Delete clothing item using clothes API
        console.log("Deleting clothing item:", selectedItemToDelete.name);
        await deleteItemMutation.mutateAsync(selectedItemToDelete._id);

        // Show success message
        Alert.alert("Success", "Item deleted successfully");

        // Refresh the clothes list
        queryClient.invalidateQueries({ queryKey: ["clothes"] });
      }

      // Close the modal
      setIsDeleteModalVisible(false);
      setSelectedItemToDelete(null);
    } catch (error) {
      console.error("Error deleting item:", error);
      const itemType = selectedItemToDelete.isOutfit ? "outfit" : "item";
      Alert.alert("Error", `Failed to delete ${itemType}. Please try again.`);
    } finally {
      setIsDeleting(false);
    }
  };

  // Handler for the Add button press
  const handleAddPress = () => {
    console.log("Add button pressed");
    setIsAddModalVisible(true);
  };

  // Handler for the Search button press
  const handleSearchPress = () => {
    console.log("Search pressed");
  };

  // Handler for the Filter button press
  const handleFilterPress = () => {
    setIsFilterModalVisible(true);
  };

  const handleFilterSelect = (
    categoryIdOrName: string,
    categoryName?: string
  ) => {
    if (activeTab === "Outfits") {
      setSelectedFilter(categoryIdOrName);
    } else {
      if (categoryName) {
        if (categoryIdOrName === "all") {
          setSelectedFilter("All");
        } else {
          const displayName = categoryName.split(" & ")[0].split(" ")[0];
          setSelectedFilter(displayName);
        }
      } else {
        setSelectedFilter(categoryIdOrName);
      }
    }
  };

  // Renders an individual item card in the grid
  const renderItem = ({ item, index }: { item: any; index: number }) => {
    // Better placeholder images for clothing items
    // Using category-based placeholders for a more realistic look
    const getPlaceholderImage = () => {
      const category = item.category?.name?.toLowerCase() || "";

      if (
        category.includes("top") ||
        category.includes("shirt") ||
        category.includes("blouse")
      ) {
        return require("@/assets/images/placeholder-item.png"); // Replace with appropriate top image
      } else if (
        category.includes("bottom") ||
        category.includes("pant") ||
        category.includes("skirt")
      ) {
        return require("@/assets/images/placeholder-item.png"); // Replace with appropriate bottom image
      } else if (category.includes("shoe") || category.includes("footwear")) {
        return require("@/assets/images/placeholder-item.png"); // Replace with appropriate shoe image
      } else if (category.includes("dress")) {
        return require("@/assets/images/placeholder-item.png"); // Replace with appropriate dress image
      } else {
        // Default fallback based on index to provide variety
        return index % 2 === 0
          ? require("@/assets/images/placeholder-item.png")
          : require("@/assets/images/placeholder-item.png");
      }
    };

    // Get the appropriate placeholder image
    const placeholderImage = getPlaceholderImage();

    // Determine the image source based on whether it's a local asset or a URI
    const getImageSource = () => {
      console.log("Getting image source for item:", {
        id: item._id,
        name: item.name,
        imageUrl: item.imageUrl,
        isLocalAsset: item.isLocalAsset,
        isOutfit: item.isOutfit,
      });

      if (!item.imageUrl) {
        console.log("No imageUrl, using placeholder");
        return placeholderImage;
      }

      if (item.isLocalAsset) {
        // For local assets (using require)
        if (
          item.imageUrl === "@/assets/images/closet/closet-outfit-main2.png"
        ) {
          console.log("Using closet-outfit-main2.png");
          return require("@/assets/images/closet/closet-outfit-main2.png");
        } else if (
          item.imageUrl === "@/assets/images/closet/closet-outfit-main3.png"
        ) {
          console.log("Using closet-outfit-main3.png");
          return require("@/assets/images/closet/closet-outfit-main3.png");
        } else {
          console.log(
            "Local asset path not recognized, using placeholder. Path was:",
            item.imageUrl
          );
          return placeholderImage;
        }
      } else {
        // For remote URIs
        console.log("Using remote URI:", item.imageUrl);
        return { uri: item.imageUrl };
      }
    };

    return (
      <ItemCard
        key={item._id || `item-${index}`}
        onLongPress={() => handleDeleteItem(item)}
        delayLongPress={500} // 500ms delay for long press
        onPress={() => {
          if (activeTab === "Outfits" && item._id && item.isOutfit) {
            // Open collage edit modal for outfits
            console.log(
              "🏠 Closet - Step 3 - Opening collage edit modal for outfit:",
              item.name
            );
            setEditingOutfit(item);
            setIsCollageModalVisible(true);
          } else if (activeTab === "Clothes" && item._id) {
            // Navigate to ClothingStats for clothes
            console.log(
              "Item clicked - Full item data:",
              JSON.stringify(item, null, 2)
            );
            console.log("Item ID:", item._id);
            console.log("Item Name:", item.name || `Item ${index + 1}`);
            console.log("Item Image URL:", item.imageUrl || "No image URL");
            console.log("Item Category:", item.category?.name || "No category");

            // Navigate to the Clothing Stats screen within the tabs group
            router.push(`/(tabs)/clothing-stats/${item._id}`);
          }
        }}
        activeOpacity={0.7}
      >
        <ItemImage
          source={getImageSource()}
          placeholder={PLACEHOLDER_BLURHASH}
          contentFit={isTablet() ? "contain" : "cover"}
          transition={300}
          cachePolicy="memory-disk"
          recyclingKey={`item-${item._id || index}`}
          style={isTablet() ? { aspectRatio: 1 } : undefined}
        />
        <ItemNameContainer>
          <ItemNameText numberOfLines={1}>
            {item.name || "Item " + (index + 1)}
          </ItemNameText>
          <ItemCategoryText numberOfLines={1}>
            {getCategoryDisplayName(item)}
          </ItemCategoryText>
        </ItemNameContainer>
      </ItemCard>
    );
  };

  return (
    <ScrollView
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}
      bounces={false}
    >
      <HeaderContainer>
        <HeaderTitle>My Closet</HeaderTitle>
        <ActionButtonsContainer>
          <IconButton onPress={handleSearchPress}>
            <SearchIcon size={16} color="#0E7E61" />
          </IconButton>
          <IconButton onPress={handleAddPress}>
            <PlusIcon size={16} color="#0E7E61" />
          </IconButton>
          {/* Demo button - will be removed once backend integration is complete */}
          {/* Demo button temporarily removed due to routing issues */}
        </ActionButtonsContainer>
      </HeaderContainer>

      <ContentContainer>
        <ToggleContainer>
          <ToggleButtonsGroup>
            <ToggleButton
              active={activeTab === "Outfits"}
              onPress={() => {
                setActiveTab("Outfits");
                // Reset filter to 'All' when switching tabs
                setSelectedFilter("All");
              }}
              style={{ paddingHorizontal: 16 }}
            >
              <Text
                style={{
                  fontFamily: "MuktaVaani-Regular",
                  fontWeight: "600",
                  fontSize: 14,
                  color: activeTab === "Outfits" ? "#FFFFFF" : "#0E7E61",
                  textAlignVertical: "center",
                  includeFontPadding: false,
                  paddingTop: 2,
                }}
              >
                Outfits
              </Text>
            </ToggleButton>
            <ToggleButton
              active={activeTab === "Clothes"}
              onPress={() => {
                setActiveTab("Clothes");
                // Reset filter to 'All' when switching tabs
                setSelectedFilter("All");
              }}
              style={{ paddingHorizontal: 16 }}
            >
              <Text
                style={{
                  fontFamily: "MuktaVaani-Regular",
                  fontWeight: "600",
                  fontSize: 14,
                  color: activeTab === "Clothes" ? "#FFFFFF" : "#0E7E61",
                  textAlignVertical: "center",
                  includeFontPadding: false,
                  paddingTop: 2,
                }}
              >
                Clothes
              </Text>
            </ToggleButton>
          </ToggleButtonsGroup>
          <FilterButton onPress={handleFilterPress}>
            <Filter size={16} color="#0E7E61" />
          </FilterButton>
        </ToggleContainer>

        {items.length > 0 && (
          <StatusText>
            {activeTab === "Outfits"
              ? `Showing ${items.length} outfits in your closet`
              : `Showing ${items.length} clothes items in your closet`}
          </StatusText>
        )}

        {isLoading ? (
          <ActivityIndicator size="large" color="#0E7E61" />
        ) : items.length > 0 ? (
          <GridContainer>
            {items.map((item: any, index: number) => {
              // Ensure each rendered item has a unique key
              const uniqueKey = item._id || `item-${index}-${Date.now()}`;
              return (
                <React.Fragment key={uniqueKey}>
                  {renderItem({ item, index })}
                </React.Fragment>
              );
            })}
          </GridContainer>
        ) : (
          <EmptyStateContainer>
            {activeTab === "Outfits" ? (
              <>
                <EmptyStateTitle>Plan an outfit</EmptyStateTitle>
                <EmptyStateImage
                  source={require("@/assets/images/closet/emptyState.png")}
                  resizeMode="contain"
                />
                <EmptyStateText>Content here</EmptyStateText>
                <ActionButton
                  primary
                  onPress={() => {
                    setIsAddModalVisible(false);
                    // Clear editing state for new outfit creation
                    setEditingOutfit(null);
                    setIsPlanOutfitModalVisible(true);
                  }}
                >
                  <ActionButtonText primary>Plan your outfit</ActionButtonText>
                </ActionButton>
              </>
            ) : (
              <>
                <EmptyStateTitle>Add new clothes</EmptyStateTitle>
                <EmptyStateImage
                  source={require("@/assets/images/closet/emptyState.png")}
                  resizeMode="contain"
                />
                <EmptyStateText>Content here</EmptyStateText>
                <ActionButton
                  primary
                  onPress={() => {
                    setIsAddModalVisible(false);
                    setTimeout(() => {
                      setIsBottomSheetModalVisible(true);
                    }, 300);
                  }}
                >
                  <ActionButtonText primary>Add new clothes</ActionButtonText>
                </ActionButton>
              </>
            )}
          </EmptyStateContainer>
        )}
      </ContentContainer>

      <SimpleAddItemModal
        isVisible={isAddModalVisible}
        onClose={() => setIsAddModalVisible(false)}
        onAddOutfit={() => {
          setIsAddModalVisible(false);
          // Clear editing state for new outfit creation
          setEditingOutfit(null);
          // Use requestAnimationFrame to ensure state updates are batched
          requestAnimationFrame(() => {
            setIsPlanOutfitModalVisible(true);
          });
        }}
        onAddClothes={() => {
          setIsAddModalVisible(false);
          // Use requestAnimationFrame to ensure state updates are batched
          requestAnimationFrame(() => {
            setIsBottomSheetModalVisible(true);
          });
        }}
      />

      <ClosetBottomSheetModal
        isVisible={isBottomSheetModalVisible}
        onClose={() => setIsBottomSheetModalVisible(false)}
        onOptionPress={(option) => {
          console.log(`🎯 Selected option: ${option}`);
          // First close the ClosetBottomSheetModal
          setIsBottomSheetModalVisible(false);

          // Handle the selected option based on the option ID
          if (option === "photo-gallery") {
            // Access the photo gallery with longer delay to ensure all modals are closed
            console.log(
              "📸 Photo gallery selected, ensuring all modals are closed..."
            );
            setTimeout(() => {
              // Double-check that no other modals are open
              console.log("📸 Modal states before ImagePicker:", {
                isAddModalVisible,
                isBottomSheetModalVisible: false, // We just set this to false
                isAddClothsModalVisible,
                isAddClothesFromGalleryModalVisible,
                isPlanOutfitModalVisible,
                isCollageModalVisible,
                isFilterModalVisible,
                isDeleteModalVisible,
              });
              accessPhotoGallery();
            }, 800); // Much longer delay
          } else if (option === "camera") {
            // Access the camera with longer delay to ensure all modals are closed
            console.log(
              "📷 Camera selected, ensuring all modals are closed..."
            );
            setTimeout(() => {
              // Double-check that no other modals are open
              console.log("📷 Modal states before ImagePicker:", {
                isAddModalVisible,
                isBottomSheetModalVisible: false, // We just set this to false
                isAddClothsModalVisible,
                isAddClothesFromGalleryModalVisible,
                isPlanOutfitModalVisible,
                isCollageModalVisible,
                isFilterModalVisible,
                isDeleteModalVisible,
              });
              accessCamera();
            }, 800); // Much longer delay
          } else if (option === "online-store") {
            setTimeout(() => {
              router.push(`/(tabs)/connect-purchases-screen`);
            }, 300);
          } else {
            // For other options, show a coming soon message
            Alert.alert("Coming Soon", `${option} - Feature coming soon!`);
          }
        }}
      />

      <PlanOutfitModal
        isVisible={isPlanOutfitModalVisible}
        onClose={() => {
          setIsPlanOutfitModalVisible(false);
          setEditingOutfit(null); // Clear editing state when closing
        }}
        onSave={(outfitData) => {
          console.log("Outfit data received from PlanOutfitModal:", outfitData);

          // The outfit has already been saved to the backend by PlanOutfitModal
          // The backend API call automatically invalidates the outfits query
          // So we don't need to do anything here - the UI will automatically update

          console.log(
            `Outfit ${
              editingOutfit ? "update" : "creation"
            } completed - UI will refresh automatically`
          );

          // Clear editing state
          setEditingOutfit(null);
        }}
        editMode={!!editingOutfit}
        existingOutfit={editingOutfit}
      />

      <CollageEditOutfitModal
        isVisible={isCollageModalVisible}
        onClose={() => {
          console.log("🏠 Closet - Step 4 - Closing collage modal");
          setIsCollageModalVisible(false);
          setEditingOutfit(null); // Clear editing state when closing
        }}
        editMode={!!editingOutfit}
        existingOutfit={editingOutfit}
      />

      {/* Bottom sheet removed for now */}

      {/* Filter modals temporarily disabled
      {activeTab === 'Outfits' ? (
        // Original filter modal for Outfits tab
        <FilterModal
          isVisible={isFilterModalVisible && activeTab === 'Outfits'}
          onClose={() => setIsFilterModalVisible(false)}
          activeTab={activeTab}
          onFilterSelect={handleFilterSelect}
          selectedFilter={selectedFilter}
          outfitCategories={outfitCategories}
          clothesCategories={clothesCategories}
        />
      ) : (
        // New filter modal with nested categories for Clothes tab
        <CategoryFilterModal
          isVisible={isFilterModalVisible && activeTab === 'Clothes'}
          onClose={() => setIsFilterModalVisible(false)}
          onSelectCategory={handleFilterSelect}
          selectedCategory={selectedFilter}
          gender={'WOMENS'} // Default to WOMENS for now
        />
      )}
      */}

      {/* Delete confirmation modal removed for now */}

      {/* Add the AddClothsModal component */}
      <AddClothsModal
        isVisible={isAddClothsModalVisible}
        selectedFilter={selectedFilter} // Pass the same filter used in the Clothes tab
        onClose={() => {
          console.log("Closing AddClothsModal...");
          setIsAddClothsModalVisible(false);
        }}
        onAdd={(selectedItems) => {
          console.log("Selected items:", selectedItems);
          // Handle the selected items
          if (selectedItems && selectedItems.length > 0) {
            // Show loading indicator
            Alert.alert(
              "Adding items",
              "Please wait while we add the selected items to your closet..."
            );

            // Create a counter for successful additions
            let successCount = 0;
            let errorCount = 0;

            // Process each item
            const processItems = async () => {
              const addItemMutation = addItem();

              // Process items sequentially to avoid race conditions
              for (const item of selectedItems) {
                try {
                  // Add the item to the closet
                  await addItemMutation.mutateAsync({
                    name: item.name,
                    itemCategoryId: item.category?._id || item.categoryId,
                    color: item.color || "",
                    brand: item.brand || "",
                    imageUrl: item.imageUrl || "",
                  });
                  successCount++;
                } catch (error) {
                  console.error(`Error adding item ${item.name}:`, error);
                  errorCount++;
                }
              }

              // Show result message
              if (successCount > 0) {
                Alert.alert(
                  "Success",
                  `Added ${successCount} items to your closet${
                    errorCount > 0 ? `. Failed to add ${errorCount} items.` : ""
                  }`,
                  [{ text: "OK" }]
                );
                // Refresh the clothes list - invalidate all clothes queries
                console.log("Invalidating clothes queries after adding items");
                queryClient.invalidateQueries({ queryKey: ["clothes"] });

                // Force a refetch of all clothes data
                setTimeout(() => {
                  console.log("Forcing refetch of clothes data");
                  queryClient.refetchQueries({ queryKey: ["clothes"] });
                }, 500);
              } else if (errorCount > 0) {
                Alert.alert(
                  "Error",
                  `Failed to add items to your closet. Please try again.`,
                  [{ text: "OK" }]
                );
              }
            };

            // Start processing items
            processItems();
          }
        }}
      />

      {/* Side Filter Panel */}
      <SideFilterPanel
        isVisible={isFilterModalVisible}
        onClose={() => setIsFilterModalVisible(false)}
        onSelectCategory={handleFilterSelect}
        selectedCategory={selectedFilter}
        activeTab={activeTab}
      />

      {/* Delete Item Modal - handles both clothes and outfits */}
      <DeleteItemModal
        isVisible={isDeleteModalVisible}
        onClose={() => {
          setIsDeleteModalVisible(false);
          setSelectedItemToDelete(null);
        }}
        onDelete={confirmDeleteItem}
        itemName={
          selectedItemToDelete?.name ||
          (selectedItemToDelete?.isOutfit ? "this outfit" : "this item")
        }
        isLoading={isDeleting}
      />

      {/* Add Clothes From Gallery Modal */}
      <AddClothesFromGalleryModal
        isVisible={isAddClothesFromGalleryModalVisible}
        onClose={() => {
          setIsAddClothesFromGalleryModalVisible(false);
          setPreSelectedImage(undefined);
        }}
        preSelectedImage={preSelectedImage}
        onSuccess={(itemId) => {
          console.log("Item added successfully with ID:", itemId);
          // Navigate to ClothingStats screen
          router.push(`/(tabs)/clothing-stats/${itemId}`);
          // Refresh the clothes list
          queryClient.invalidateQueries({ queryKey: ["clothes"] });
        }}
      />
    </ScrollView>
  );
}
